import pandas as pd

df = pd.read_csv("full_dataset.csv")


df.head

df.columns

first_row = df.iloc[0]
for column, value in first_row.items():
    print(f"{column}: {value}")


def search_and_display(query):
    # Find rows where the title contains the query string (case-insensitive)
    matched_rows = df[df['title'].str.contains(query, case=False, na=False)]
    
    if matched_rows.empty:
        print(f"No recipes found with title matching '{query}'.")
        return
    
    # Display each matched row in column-wise format
    for idx, row in matched_rows.iterrows():
        for column, value in row.items():
            print(f"{column}: {value}")
        print("\n" + "-"*20 + "\n")

# Example usage
query = input("Enter recipe title to search: ")
search_and_display(query)

def search_and_display_exact(query):
    # Exact match (case-insensitive)
    matched_rows = df[df['title'].str.lower() == query.lower()]
    
    if matched_rows.empty:
        print(f"No recipes found with title exactly matching '{query}'.")
        return
    
    # Get the first matching row only
    first_row = matched_rows.iloc[0]
    
    for column, value in first_row.items():
        print(f"{column}: {value}")
    print("\n" + "-"*20 + "\n")

# Example usage
query = input("Enter recipe title to search (exact match): ")
search_and_display_exact(query)


len(df)

import pandas as pd
import json

# Load JSON data from a file
with open('country_cuisines.json', 'r') as json_file:
    data = json.load(json_file)

# Convert JSON to DataFrame
df1 = pd.json_normalize(data)  # handles nested JSON if needed

# Save DataFrame to CSV
df1.to_csv('output.csv', index=False)

print("JSON has been converted to CSV and saved as 'output.csv'")




import json
import pandas as pd

# Load dataset
df = pd.read_csv("full_dataset.csv")

# Ensure 'title' and 'directions' columns exist
if 'title' not in df.columns or 'directions' not in df.columns:
    raise ValueError("dataset.csv must contain 'title' and 'directions' columns")

# Load dish data
with open("checkall_country_dishes_cleaned.json", "r", encoding="utf-8") as f:
    country_dishes = json.load(f)


# Normalize title column for matching
df['title_lower'] = df['title'].str.lower()


# Prepare final result
final_data = []

import re

for country_entry in country_dishes:
    country_name = country_entry["country"]
    dish_list = country_entry["dishes"]

    print(f"\n🌍 Processing country: {country_name}")
    country_result = {"country": country_name, "dishes": []}

    for dish in dish_list:
        print(f"  🍽️ Checking dish: {dish}")
        dish_lower = dish.lower()
        matched_row = df[df['title_lower'].str.contains(re.escape(dish_lower), na=False)]

        if not matched_row.empty:
            direction_text = matched_row.iloc[0]['directions']
            print(f"    ✅ Match found: {matched_row.iloc[0]['title']}")
            country_result["dishes"].append({
                "name": dish,
                "recipe": direction_text.strip()
            })
        else:
            print(f"    ❌ No match found.")
            country_result["dishes"].append({
                "name": dish,
                "recipe": None
            })

    final_data.append(country_result)


# Save the result
with open("country_dishes_with_recipes.json", "w", encoding="utf-8") as f:
    json.dump(final_data, f, indent=2, ensure_ascii=False)


print("✅ Done! Saved enriched data to country_dishes_with_recipes.json")


import json
import pandas as pd
import re
from collections import defaultdict

# Load missing ingredients dish list
with open("missing_ingredients.json", "r", encoding="utf-8") as f:
    flat_dish_data = json.load(f)

# Load CSV dataset
df = pd.read_csv("full_dataset.csv")

# Normalize for matching
df['title_lower'] = df['title'].str.lower()

# Grouped results per country
grouped_results = defaultdict(list)

# Helper to find ingredients by dish name
def find_ingredients(dish_name):
    matched = df[df['title_lower'].str.contains(re.escape(dish_name.lower()), na=False)]
    if not matched.empty:
        return matched.iloc[0]['ingredients'].strip()
    return None

# Process each entry
for entry in flat_dish_data:
    country = entry["country"]
    dish = entry["dish"]
    print(f"🔍 Searching ingredients for {dish} ({country})...")

    ingredients = find_ingredients(dish)

    grouped_results[country].append({
        "name": dish,
        "ingredients": ingredients
    })

# Convert to structured format
final_output = [
    {
        "country": country,
        "dishes": dishes
    }
    for country, dishes in grouped_results.items()
]

# Save result
with open("recovered_country_dishes_with_ingredients.json", "w", encoding="utf-8") as f:
    json.dump(final_output, f, indent=2, ensure_ascii=False)

print("✅ Done! Saved to recovered_country_dishes_with_ingredients.json")


import json

# Load the enriched dish data
with open("country_dishes_with_recipes.json", "r", encoding="utf-8") as f:
    data = json.load(f)

total_dishes = 0

for entry in data:
    country = entry["country"]
    dish_count = len(entry["dishes"])
    total_dishes += dish_count
    print(f"🌍 {country}: {dish_count} dishes")

print(f"\n🍽️ Total dishes across all countries: {total_dishes}")


import os
import json
from collections import defaultdict
from openai import OpenAI
from dotenv import load_dotenv
from time import sleep

# Load API key
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI_API_KEY not found in .env")

client = OpenAI(api_key=api_key)

# Load all missing dishes
with open("missing_recipes.json", "r", encoding="utf-8") as f:
    all_missing_dishes = json.load(f)

# Split into batches of 10 dishes
def chunk_list(lst, size):
    for i in range(0, len(lst), size):
        yield lst[i:i + size]

# Prepare result container
combined_results = []

# Process each batch
for batch_index, batch in enumerate(chunk_list(all_missing_dishes, 10), start=1):
    print(f"\n🔄 Processing batch {batch_index} ({len(batch)} dishes)...")

    # Group this batch by country
    country_map = defaultdict(list)
    for entry in batch:
        country_map[entry["country"]].append(entry["dish"])

    # One GPT request per country in this batch
    for country, dishes in country_map.items():
        print(f"\n🌍 Country: {country} | Dishes: {len(dishes)}")

        # Build prompt
        dish_list_str = "\n".join([f"- {dish}" for dish in dishes])
        prompt = f"""
You are a culinary expert.

Return the recipes for the following traditional dishes from {country}. Format the response strictly as JSON like this:

{{
  "country": "{country}",
  "dishes": [
    {{
      "name": "Dish Name",
      "recipe": "[\\"Step 1\\", \\"Step 2\\", \\"Step 3\\"]"
    }},
    ...
  ]
}}

🔸 The value of "recipe" must be a **string that looks like a JSON array** — not a real array.
🔸 Use double quotes inside the recipe string and escape them correctly (\\").
🔸 Each step must be a clear, short instruction.
🔸 Return only valid JSON — no comments or explanation.

Dishes to process:
{dish_list_str}
"""

        # GPT API call
        try:
            response = client.chat.completions.create(
                model="gpt-3.5-turbo-16k",
                messages=[{"role": "user", "content": prompt.strip()}],
                temperature=0.3
            )

            reply = response.choices[0].message.content.strip()
            print(f"✅ Received response for {country} in batch {batch_index}.")

            # Parse response
            parsed = json.loads(reply)

            for dish in parsed["dishes"]:
                if not isinstance(dish["recipe"], str):
                    print(f"⚠️ Recipe for '{dish['name']}' in {country} is not a string.")

            combined_results.append(parsed)

        except Exception as e:
            print(f"❌ Error in batch {batch_index} for {country}: {e}")
            sleep(5)  # wait before continuing

# Save the combined result
with open("gpt_missing_recipes_all.json", "w", encoding="utf-8") as f:
    json.dump(combined_results, f, indent=2, ensure_ascii=False)

print("\n✅ All batches processed. Final data saved to gpt_missing_recipes_all.json")


import os
import json
from collections import defaultdict
from openai import OpenAI
from dotenv import load_dotenv
from time import sleep

# Load API key
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI_API_KEY not found in .env")

client = OpenAI(api_key=api_key)

# Load missing ingredients data
with open("missing_ingredients.json", "r", encoding="utf-8") as f:
    all_dishes = json.load(f)

# Batch utility
def chunk_list(lst, size):
    for i in range(0, len(lst), size):
        yield lst[i:i + size]

final_results = []

# Process in batches
for batch_index, batch in enumerate(chunk_list(all_dishes, 10), start=1):
    print(f"\n🔁 Processing batch {batch_index} with {len(batch)} dishes...")

    country_map = defaultdict(list)
    for entry in batch:
        country_map[entry["country"]].append(entry)

    for country, dishes in country_map.items():
        print(f"🌍 Country: {country} — {len(dishes)} dishes")

        input_block = ""
        for d in dishes:
            input_block += f"- {d['dish']}\n  Recipe: {d['recipe']}\n"

        # GPT Prompt
        prompt = f"""
You are a culinary expert.

Using the recipes provided, return the most likely ingredients for each traditional dish from {country}.
Each ingredient list should be returned as a **stringified JSON array** of short phrases, like:

[
  "2 cups flour",
  "1 tsp sugar",
  "1/2 cup water"
]

⚠️ Your response format MUST be:

{{
  "country": "{country}",
  "dishes": [
    {{
      "name": "Dish Name",
      "ingredients": "[\\"ingredient 1\\", \\"ingredient 2\\"]"
    }},
    ...
  ]
}}

Strict instructions:
- Escape double quotes inside ingredients with a backslash (`\\`)
- Return ingredients as a string that looks like a list — not a real array
- Do not include explanations or commentary

Here are the dishes:

{input_block}
"""

        try:
            response = client.chat.completions.create(
                model="gpt-3.5-turbo-16k",
                messages=[{"role": "user", "content": prompt.strip()}],
                temperature=0.3
            )

            reply = response.choices[0].message.content.strip()
            print(f"✅ GPT response received for {country}.")

            gpt_data = json.loads(reply)

            # Merge back the original recipes
            merged = []
            for gpt_dish in gpt_data["dishes"]:
                original = next((d for d in dishes if d["dish"].lower() == gpt_dish["name"].lower()), None)
                merged.append({
                    "name": gpt_dish["name"],
                    "recipe": original["recipe"] if original else None,
                    "ingredients": gpt_dish["ingredients"]
                })

            final_results.append({
                "country": country,
                "dishes": merged
            })

        except Exception as e:
            print(f"❌ Error for {country} in batch {batch_index}: {e}")
            sleep(5)

# Save the results
with open("New_gpt_ingredients_with_recipes.json", "w", encoding="utf-8") as f:
    json.dump(final_results, f, indent=2, ensure_ascii=False)

print("\n✅ All batches completed. Results saved to gpt_ingredients_with_recipes.json")


import json

# Load original data with missing recipes
with open("All_DATA.json", "r", encoding="utf-8") as f:
    original_data = json.load(f)

# Load GPT-generated recipes
with open("gpt_missing_recipes_all.json", "r", encoding="utf-8") as f:
    gpt_data = json.load(f)

# Convert GPT data to a dict: {country: {dish_name: recipe}}
gpt_lookup = {}
for country_entry in gpt_data:
    country = country_entry["country"]
    gpt_lookup[country] = {
        dish["name"]: dish["recipe"] for dish in country_entry["dishes"]
    }

# Merge GPT recipes into original
for original_country in original_data:
    country_name = original_country["country"]
    country_dishes = original_country["dishes"]

    # Only process if GPT has data for this country
    if country_name not in gpt_lookup:
        continue

    # Get GPT dishes for this country
    gpt_dishes = gpt_lookup[country_name]

    existing_dish_names = {dish["name"] for dish in country_dishes}
    updated_dish_names = set()

    # Update existing dishes if recipe is null and GPT has recipe
    for dish in country_dishes:
        if dish["recipe"] is None and dish["name"] in gpt_dishes:
            dish["recipe"] = gpt_dishes[dish["name"]]
            updated_dish_names.add(dish["name"])

    # Add new dishes from GPT that don't exist in original
    for gpt_dish_name, gpt_recipe in gpt_dishes.items():
        if gpt_dish_name not in existing_dish_names:
            print(f"➕ Adding new dish: {gpt_dish_name} to {country_name}")
            country_dishes.append({
                "name": gpt_dish_name,
                "recipe": gpt_recipe
            })

# Save merged output
with open("All_DATA_final.json", "w", encoding="utf-8") as f:
    json.dump(original_data, f, indent=2, ensure_ascii=False)

print("✅ Merging complete. Saved to country_dishes_merged.json")


import json

# Load JSON-2: your main dataset (with or without ingredients)
with open("Data_ingredients.json", "r", encoding="utf-8") as f:
    main_data = json.load(f)

# Load JSON-1: GPT-generated data that includes ingredients
with open("New_gpt_ingredients_with_recipes.json", "r", encoding="utf-8") as f:
    gpt_data = json.load(f)

# Build a lookup: {(country, dish_name) -> ingredients}
gpt_lookup = {}
for entry in gpt_data:
    country = entry["country"]
    for dish in entry["dishes"]:
        key = (country.strip().lower(), dish["name"].strip().lower())
        gpt_lookup[key] = dish["ingredients"]

# Merge logic
updated_count = 0

for country_entry in main_data:
    country = country_entry["country"]
    for dish in country_entry["dishes"]:
        key = (country.strip().lower(), dish["name"].strip().lower())

        if dish.get("ingredients") in [None, "null"] and key in gpt_lookup:
            dish["ingredients"] = gpt_lookup[key]
            updated_count += 1
            print(f"✅ Added ingredients for: {dish['name']} ({country})")

# Save the merged file
with open("Final_Data_ingredients.json", "w", encoding="utf-8") as f:
    json.dump(main_data, f, indent=2, ensure_ascii=False)

print(f"\n✅ Done! Ingredients added to {updated_count} dishes.")
print("📁 Saved to country_dishes_merged_with_ingredients.json")


import json
import pandas as pd
import re

# Load the CSV dataset
df = pd.read_csv("full_dataset.csv")

# Ensure required columns exist
if 'title' not in df.columns or 'ingredients' not in df.columns:
    raise ValueError("CSV must have 'title' and 'ingredients' columns.")

# Normalize titles for matching
df['title_lower'] = df['title'].str.lower()

# Load the country dishes JSON
with open("FINALIZED_DATA.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# Add ingredients to each dish
for country_entry in data:
    country_name = country_entry["country"]
    print(f"\n🌍 Processing country: {country_name}")

    for dish in country_entry["dishes"]:
        dish_name = dish["name"]
        print(f"  🍽️ Matching dish: {dish_name}")

        # Case-insensitive match
        matched = df[df['title_lower'].str.contains(re.escape(dish_name.lower()), na=False)]

        if not matched.empty:
            ingredients = matched.iloc[0]['ingredients']
            dish["ingredients"] = ingredients.strip() if isinstance(ingredients, str) else None
            print(f"    ✅ Ingredients added.")
        else:
            dish["ingredients"] = None
            print(f"    ❌ Ingredients not found.")

# Save the updated JSON
with open("country_dishes_with_ingredients.json", "w", encoding="utf-8") as f:
    json.dump(data, f, indent=2, ensure_ascii=False)

print("\n✅ Done! Ingredients added and saved to country_dishes_with_ingredients.json")


import json

# Load the original structured data
with open("Data_ingredients.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# Convert to required format
output = []

for entry in data:
    country = entry["country"]
    dishes = [dish["name"] for dish in entry.get("dishes", []) if dish.get("name")]

    output.append({
        "country": country,
        "dishes": dishes
    })

# Save to file
with open("country_dish_list.json", "w", encoding="utf-8") as f:
    json.dump(output, f, indent=2, ensure_ascii=False)

print(f"✅ Done! Extracted dish names for {len(output)} countries.")
print("📁 Saved to: country_dish_list.json")


pip install selenium>=4.10.0 webdriver-manager>=4.0.1


import json
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# -----------------------------
# Output file path
output_file = "Dish_image_results.json"

# If file doesn't exist, create and initialize as list
if not os.path.exists(output_file):
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump([], f, ensure_ascii=False, indent=2)

# Load already processed entries to avoid duplicates
with open(output_file, "r", encoding="utf-8") as f:
    existing_data = json.load(f)

processed_pairs = {(item["country"], item["dish"]) for item in existing_data}
# -----------------------------

# Load input JSON
with open("country_dish_list.json", "r", encoding="utf-8") as f:
    country_dishes = json.load(f)

# Setup Selenium WebDriver
options = webdriver.ChromeOptions()
options.add_argument("--start-maximized")
#options.add_argument('--headless')  # Uncomment if running on server/headless
# options.add_argument('--no-sandbox')
# options.add_argument('--disable-gpu')

driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

try:
    for entry in country_dishes:
        country = entry["country"]
        dishes = entry.get("dishes", [])

        for dish in dishes:
            if (country, dish) in processed_pairs:
                print(f"⏩ Already processed: {country} - {dish}")
                continue

            query = f"{country} {dish} traditional food"
            print(f"\n🔍 Searching images for: {query}")

            driver.get("https://www.google.com/imghp")
            search_box = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "q"))
            )
            search_box.clear()
            search_box.send_keys(query + Keys.RETURN)

            time.sleep(3)
            image_urls = []

            for i in range(1, 4):
                try:
                    print(f"  📸 Image {i}...")
                    image_div_xpath = f"/html/body/div[3]/div/div[14]/div/div[2]/div[2]/div/div/div/div/div[1]/div/div/div[{i}]"
                    image_div = driver.find_element(By.XPATH, image_div_xpath)
                    image_div.click()
                    time.sleep(3)

                    preview_div = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[@class='BIB1wf EIehLd fHE6De Emjfjd' and @jsname='CGzTgf']"))
                    )

                    img_element = WebDriverWait(preview_div, 5).until(
                        EC.presence_of_element_located((By.XPATH, ".//img[contains(@class, 'sFlh5c FyHeAf iPVvYb')]"))
                    )

                    image_src = img_element.get_attribute("src")
                    if image_src:
                        image_urls.append(image_src)
                        print(f"     ✅ Found: {image_src}")

                except Exception as e:
                    print(f"     ⚠️ Failed to fetch image {i}: {e}")
                    continue

            # Prepare result
            result_entry = {
                "country": country,
                "dish": dish,
                "images": image_urls if image_urls else None
            }

            # Append to file
            with open(output_file, "r+", encoding="utf-8") as f:
                data = json.load(f)
                data.append(result_entry)
                f.seek(0)
                json.dump(data, f, indent=2, ensure_ascii=False)
                f.truncate()

            print(f"✅ Saved: {country} - {dish}")

except Exception as e:
    print("❌ General error:", e)

finally:
    driver.quit()
    print("\n🎉 All done! Browser closed.")


import os
import json
import time
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Config
NUM_THREADS = 2
MAX_RETRIES = 3
BASE_OUTPUT_FOLDER = "Threads_data"
PROGRESS_FOLDER = os.path.join(BASE_OUTPUT_FOLDER, "progress")

os.makedirs(BASE_OUTPUT_FOLDER, exist_ok=True)
os.makedirs(PROGRESS_FOLDER, exist_ok=True)

# Load input and split
with open("nested_dish_list.json", "r", encoding="utf-8") as f:
    all_entries = json.load(f)

chunk_size = len(all_entries) // NUM_THREADS
chunks = [all_entries[i * chunk_size: (i + 1) * chunk_size] for i in range(NUM_THREADS)]
if len(all_entries) % NUM_THREADS:
    chunks[-1].extend(all_entries[NUM_THREADS * chunk_size:])


def append_json_line_safe(path, item):
    """Append item to JSON array in a file."""
    if not os.path.exists(path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump([item], f, ensure_ascii=False, indent=2)
    else:
        with open(path, "r+", encoding="utf-8") as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError:
                data = []
            data.append(item)
            f.seek(0)
            json.dump(data, f, ensure_ascii=False, indent=2)
            f.truncate()


def load_progress(progress_path):
    """Load progress JSON in full format."""
    if os.path.exists(progress_path):
        with open(progress_path, "r", encoding="utf-8") as pf:
            try:
                return json.load(pf)
            except:
                return []
    return []


def save_progress(progress_path, progress_list):
    """Save list of processed entries."""
    with open(progress_path, "w", encoding="utf-8") as pf:
        json.dump(progress_list, pf, indent=2, ensure_ascii=False)


def scrape_images(thread_id, entries):
    thread_name = f"Thread{thread_id + 1}"
    output_path = os.path.join(BASE_OUTPUT_FOLDER, f"{thread_name}.json")
    progress_path = os.path.join(PROGRESS_FOLDER, f"{thread_name}_progress.json")

    # Load progress
    completed = load_progress(progress_path)
    completed_lookup = {(entry["country"], dish) for entry in completed for dish in entry["dishes"]}

    # Selenium setup
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    # options.add_argument("--headless")
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    try:
        for entry in entries:
            country = entry["country"]
            dishes = entry.get("dishes", [])

            completed_dishes = [dish for (c, dish) in completed_lookup if c == country]
            remaining_dishes = [dish for dish in dishes if (country, dish) not in completed_lookup]

            if not remaining_dishes:
                continue

            country_results = []

            for dish in remaining_dishes:
                query = f"{country} {dish} traditional food"
                print(f"[{thread_name}] 🔍 {query}")

                image_urls = []
                retries = 0

                while retries < MAX_RETRIES and len(image_urls) < 3:
                    try:
                        driver.get("https://www.google.com/imghp")
                        search_box = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.NAME, "q"))
                        )
                        search_box.clear()
                        search_box.send_keys(query + Keys.RETURN)
                        time.sleep(3)

                        image_urls = []
                        for i in range(1, 4):
                            try:
                                xpath = f"/html/body/div[3]/div/div[14]/div/div[2]/div[2]/div/div/div/div/div[1]/div/div/div[{i}]"
                                image_div = driver.find_element(By.XPATH, xpath)
                                image_div.click()
                                time.sleep(2)

                                preview = WebDriverWait(driver, 5).until(
                                    EC.presence_of_element_located((By.XPATH, "//div[@class='BIB1wf EIehLd fHE6De Emjfjd' and @jsname='CGzTgf']"))
                                )
                                img = WebDriverWait(preview, 5).until(
                                    EC.presence_of_element_located((By.XPATH, ".//img[contains(@class, 'sFlh5c FyHeAf iPVvYb')]"))
                                )
                                src = img.get_attribute("src")
                                if src:
                                    image_urls.append(src)
                                    print(f"[{thread_name}] ✅ Image {i}: {src}")
                            except Exception as e:
                                print(f"[{thread_name}] ⚠️ Image {i} failed: {e}")
                                continue

                        if len(image_urls) < 3:
                            retries += 1
                            print(f"[{thread_name}] 🔁 Retry {retries} for {query}")
                            time.sleep(2)
                    except Exception as e:
                        print(f"[{thread_name}] ❌ Error on {query}: {e}")
                        retries += 1

                result_entry = {
                    "country": country,
                    "dish": dish,
                    "images": image_urls if image_urls else None
                }
                append_json_line_safe(output_path, result_entry)

                # Update progress in memory
                for progress_entry in completed:
                    if progress_entry["country"] == country:
                        if dish not in progress_entry["dishes"]:
                            progress_entry["dishes"].append(dish)
                        break
                else:
                    completed.append({"country": country, "dishes": [dish]})

                save_progress(progress_path, completed)

    except Exception as e:
        print(f"[{thread_name}] ❌ Fatal error: {e}")

    finally:
        driver.quit()
        print(f"[{thread_name}] 🧹 Browser closed.")


# Start all threads
threads = []
for i in range(NUM_THREADS):
    t = threading.Thread(target=scrape_images, args=(i, chunks[i]))
    threads.append(t)
    t.start()

for t in threads:
    t.join()

print("\n✅ All threads completed.")


import json
import os

# Path to thread files
base_dir = "Threads_data"
output_file = "Again_merging.json"
merged_data = []

# Read and merge data from Thread1.json to Thread5.json
for i in range(1, 3):
    thread_file = os.path.join(base_dir, f"Thread{i}.json")
    if os.path.exists(thread_file):
        with open(thread_file, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                merged_data.extend(data)
                print(f"✅ Loaded {len(data)} items from Thread{i}.json")
            except Exception as e:
                print(f"❌ Failed to load Thread{i}.json: {e}")
    else:
        print(f"⚠️ File not found: {thread_file}")

# Sort by country, then dish
merged_data.sort(key=lambda x: (x["country"].lower(), x["dish"].lower()))

# Save to final output file
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(merged_data, f, indent=2, ensure_ascii=False)

print(f"\n✅ Merged and sorted data saved to: {output_file}")


import json

# Load Final.json (target data)
with open("Final.json", "r", encoding="utf-8") as f:
    final_data = json.load(f)

# Load Again_merging.json (source of images)
with open("Again_merging.json", "r", encoding="utf-8") as f:
    image_data = json.load(f)

# Build lookup: {(country.lower().strip(), dish.lower().strip()): images}
image_lookup = {
    (entry["country"].strip().lower(), entry["dish"].strip().lower()): entry.get("images")
    for entry in image_data
}

# Inject images where missing
for entry in final_data:
    country = entry.get("country", "").strip()
    for dish in entry.get("dishes", []):
        name = dish.get("name", "").strip()
        key = (country.lower(), name.lower())

        # Only insert if images is null or not present
        if dish.get("images") is None:
            if key in image_lookup:
                dish["images"] = image_lookup[key]
            else:
                dish["images"] = None  # ensure key exists, even if not found

# Save updated file
with open("SecondLast.json", "w", encoding="utf-8") as f:
    json.dump(final_data, f, indent=2, ensure_ascii=False)

print("✅ Done: Missing images filled. Output saved to SecondLast.json")


import json
from collections import defaultdict

# Load input data
with open("missing_images.json", "r", encoding="utf-8") as f:
    flat_data = json.load(f)

# Group by country
grouped = defaultdict(list)
for entry in flat_data:
    country = entry["country"]
    dish = entry["dish"]
    grouped[country].append(dish)

# Convert to desired format
nested_format = [{"country": country, "dishes": dishes} for country, dishes in grouped.items()]

# Save output
with open("nested_dish_list.json", "w", encoding="utf-8") as f:
    json.dump(nested_format, f, indent=2, ensure_ascii=False)

print("✅ Converted and saved to nested_dish_list.json")
