# ALL COUNTRIES CUISINES Dataset

A comprehensive dataset containing traditional dishes from 191 countries worldwide, complete with recipes, ingredients, and images. This project systematically collects, processes, and enriches culinary data to create the most extensive global cuisine database.

## Helping Dataset Used
Used this dataset also: https://recipenlg.cs.put.poznan.pl/dataset
## 📊 Dataset Overview

The final dataset (`dataset/ALL_COUNTRIES_CUISINES.json`) contains:
- **191 countries** from Afghanistan to Zimbabwe
- **Thousands of traditional dishes** with authentic recipes
- **Detailed ingredients lists** for each dish
- **Multiple high-quality images** for visual reference
- **Structured JSON format** for easy integration

## 🏗️ Project Architecture

### Core Scripts

#### 1. `countries.py`
**Purpose**: Generates the foundational list of world countries
- Uses OpenAI GPT-3.5-turbo to generate a comprehensive list of all recognized countries
- Outputs a clean JSON array of 191 country names
- Creates `countries.json` as the base for all subsequent processing

#### 2. `cuisines.py`
**Purpose**: Fetches traditional dishes for each country
- Processes countries in batches of 3 for efficient API usage
- Uses OpenAI GPT-3.5-turbo-16k for comprehensive dish collection
- Implements retry logic and error handling
- Deduplicates dishes within each country
- Outputs `checkall_country_dishes_cleaned.json`

### Data Processing Pipeline

#### 3. `dataset/Recepie.py`
**Purpose**: Enriches dishes with detailed recipes
- Matches dish names with a comprehensive recipe database (`full_dataset.csv`)
- Uses fuzzy string matching for accurate recipe assignment
- Creates `country_dishes_with_recipes.json`

#### 4. `dataset/All_cuisines_script.ipynb`
**Purpose**: Multi-stage data processing and enrichment
- **Stage 1**: CSV to JSON conversion for recipe data
- **Stage 2**: Recipe matching and integration
- **Stage 3**: Ingredient extraction and assignment
- **Stage 4**: Data merging and consolidation
- **Stage 5**: Image collection and integration
- **Stage 6**: Final data structuring and validation

#### 5. `dataset/checking_missing.py`
**Purpose**: Quality assurance and data validation
- Identifies dishes missing images or other required fields
- Generates reports for data completeness
- Creates `missing_images.json` for targeted improvements

## 🔄 Data Flow Workflow

```
1. countries.py → countries.json (191 countries)
                     ↓
2. cuisines.py → checkall_country_dishes_cleaned.json (countries + dishes)
                     ↓
3. Recepie.py → country_dishes_with_recipes.json (+ recipes)
                     ↓
4. All_cuisines_script.ipynb → Multiple processing stages:
   - Recipe matching with full_dataset.csv
   - Ingredient extraction and assignment
   - Image collection via web scraping
   - Data merging and consolidation
                     ↓
5. Final Output → ALL_COUNTRIES_CUISINES.json
```

## 📁 File Structure

```
ALL_COUNTRIES_CUISINES/
├── countries.py              # Country list generation
├── cuisines.py              # Dish collection per country
├── countries.json           # List of 191 countries
├── requirements.txt         # Python dependencies
├── readme.md               # This documentation
├── dataset/
│   ├── ALL_COUNTRIES_CUISINES.json    # 🎯 FINAL DATASET
│   ├── All_cuisines_script.ipynb      # Main processing pipeline
│   ├── Recepie.py                     # Recipe matching script
│   ├── checking_missing.py            # Data validation
│   ├── full_dataset.csv               # Recipe database
│   ├── Again_merging.json             # Image data source
│   ├── Data_ingredients.json          # Ingredient-enriched data
│   ├── FINALIZED_DATA.json           # Pre-final processing
│   ├── Final.json                     # Near-final version
│   ├── Dish_image_results.json        # Image collection results
│   └── Threads_data/                  # Processing threads data
└── venv/                             # Python virtual environment
```

## 🎯 Final Dataset Structure

The `ALL_COUNTRIES_CUISINES.json` file follows this structure:

```json
[
  {
    "country": "Afghanistan",
    "dishes": [
      {
        "name": "Kabuli Pulao",
        "recipe": "[\"Step 1: Wash and pat the meat dry...\", \"Step 2: Heat oil...\"]",
        "ingredients": "[\"1.5 lb leg of lamb\", \"1/4 cup vegetable oil\", \"1 medium onion\"]",
        "images": [
          "https://example.com/image1.jpg",
          "https://example.com/image2.jpg",
          "https://example.com/image3.jpg"
        ]
      }
    ]
  }
]
```

### Data Fields:
- **country**: Country name (string)
- **dishes**: Array of dish objects
  - **name**: Dish name (string)
  - **recipe**: Step-by-step cooking instructions (JSON string array)
  - **ingredients**: List of ingredients with quantities (JSON string array)
  - **images**: Array of image URLs (array of strings)

## 🛠️ Technical Implementation

### Dependencies
```
openai>=1.0.0          # AI-powered content generation
python-dotenv>=1.0.0   # Environment variable management
selenium>=4.10.0       # Web scraping for images
webdriver-manager>=4.0.1  # Browser automation
pandas>=2.0.0          # Data processing
jupyter>=1.0.0         # Notebook processing
```

### Key Features

#### 1. **AI-Powered Content Generation**
- Uses OpenAI's GPT models for authentic dish discovery
- Implements intelligent batching for API efficiency
- Includes retry mechanisms for reliability

#### 2. **Advanced Data Matching**
- Fuzzy string matching for recipe assignment
- Cross-referencing multiple data sources
- Intelligent deduplication algorithms

#### 3. **Web Scraping Integration**
- Automated image collection using Selenium
- Multiple image sources per dish
- Quality validation and filtering

#### 4. **Data Quality Assurance**
- Comprehensive validation scripts
- Missing data identification
- Automated quality reports

## 🚀 Usage Instructions

### Setup
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Set up OpenAI API key in `.env` file:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

### Running the Pipeline
1. **Generate countries**: `python countries.py`
2. **Collect dishes**: `python cuisines.py`
3. **Add recipes**: `python dataset/Recepie.py`
4. **Process and enrich**: Run `dataset/All_cuisines_script.ipynb`
5. **Validate data**: `python dataset/checking_missing.py`

### Using the Dataset
```python
import json

# Load the complete dataset
with open('dataset/ALL_COUNTRIES_CUISINES.json', 'r', encoding='utf-8') as f:
    cuisines_data = json.load(f)

# Example: Get all dishes from Italy
italy_dishes = next(country['dishes'] for country in cuisines_data
                   if country['country'] == 'Italy')

# Example: Search for a specific dish
def find_dish(dish_name):
    for country in cuisines_data:
        for dish in country['dishes']:
            if dish_name.lower() in dish['name'].lower():
                return {
                    'country': country['country'],
                    'dish': dish
                }
    return None
```

## 📈 Dataset Statistics

- **Total Countries**: 191
- **Total Dishes**: ~28,000+ (varies by processing stage)
- **Data Completeness**:
  - Recipes: ~95%
  - Ingredients: ~90%
  - Images: ~85%
- **File Size**: ~29MB (final JSON)
- **Processing Time**: ~6-8 hours (full pipeline)

## 🔍 Data Sources

1. **OpenAI GPT Models**: Dish names and cultural authenticity
2. **Recipe Database**: `full_dataset.csv` containing 50,000+ recipes
3. **Web Scraping**: Multiple culinary websites for images
4. **Manual Curation**: Quality validation and corrections

## 🎯 Use Cases

- **Culinary Research**: Academic studies on global food culture
- **Recipe Applications**: Mobile apps and cooking platforms
- **Cultural Analysis**: Understanding food traditions worldwide
- **Machine Learning**: Training models for food recognition
- **Educational Content**: Teaching about world cuisines
- **Restaurant Industry**: Menu inspiration and authenticity verification

## 🤝 Contributing

This dataset represents a comprehensive effort to document world cuisines. For updates or corrections:
1. Identify specific data issues
2. Provide authentic sources for corrections
3. Follow the established data structure
4. Validate changes through the processing pipeline

## 📄 License

This dataset is created for educational and research purposes. Please respect the cultural significance of traditional recipes and acknowledge sources when using this data.

---

*Last Updated: July 2025*
*Total Processing Time: ~40 hours*
*Data Accuracy: Continuously validated and improved*